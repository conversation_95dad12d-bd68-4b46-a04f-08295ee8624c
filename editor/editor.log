This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.22)  31 MAY 2025 06:49
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**editor.tex
(./editor.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./svmult.cls
Document Class: svmult 2024/03/15 v5.11 
Springer Verlag global LaTeX document class for multi authored books
Class Springer-SVMult Info: extra/valid Springer sub-package 
(Springer-SVMult)           not found in option list - using "global" style.
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
\svparindent=\dimen142
\bibindent=\dimen143
\betweenumberspace=\dimen144
\headlineindent=\dimen145
\minitoc=\write3
\c@minitocdepth=\count270
\c@chapter=\count271
\mottowidth=\dimen146
\svitemindent=\dimen147
\verbatimindent=\dimen148
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 975.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 976.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 977.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 978.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 979.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 980.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 981.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 982.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 983.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 984.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 985.
\tocchpnum=\dimen149
\tocsecnum=\dimen150
\tocsectotal=\dimen151
\tocsubsecnum=\dimen152
\tocsubsectotal=\dimen153
\tocsubsubsecnum=\dimen154
\tocsubsubsectotal=\dimen155
\tocparanum=\dimen156
\tocparatotal=\dimen157
\tocsubparanum=\dimen158
\foot@parindent=\dimen159
\c@theorem=\count272
\c@case=\count273
\c@conjecture=\count274
\c@corollary=\count275
\c@definition=\count276
\c@example=\count277
\c@exercise=\count278
\c@lemma=\count279
\c@note=\count280
\c@problem=\count281
\c@property=\count282
\c@proposition=\count283
\c@question=\count284
\c@solution=\count285
\c@remark=\count286
\c@prob=\count287
\instindent=\dimen160
\figgap=\dimen161
\bildb@x=\box52
\figcapgap=\dimen162
\tabcapgap=\dimen163
\c@merk=\count288
\c@@inst=\count289
\c@@auth=\count290
\c@auco=\count291
\instindent=\dimen164
\authrun=\box53
\authorrunning=\toks17
\tocauthor=\toks18
\titrun=\box54
\titlerunning=\toks19
\toctitle=\toks20
\c@contribution=\count292
LaTeX Info: Redefining \abstract on input line 2385.

(/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/x11nam.def
File: x11nam.def 2024/09/29 v3.02 Predefined colors according to Unix/X11 (UK)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks21
\thm@bodyfont=\toks22
\thm@headfont=\toks23
\thm@notefont=\toks24
\thm@headpunct=\toks25
\thm@preskip=\skip51
\thm@postskip=\skip52
\thm@headsep=\skip53
\dth@everypar=\toks26
LaTeX Info: Redefining \qed on input line 273.
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/framed/framed.sty
Package: framed 2011/10/22 v 0.96: framed or shaded text with page breaks
\OuterFrameSep=\skip54
\fb@frw=\dimen165
\fb@frh=\dimen166
\FrameRule=\dimen167
\FrameSep=\dimen168
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/makeidx.sty
Package: makeidx 2021/10/04 v1.0m Standard LaTeX package
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks27
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen169
\Gin@req@width=\dimen170
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count293
\mult@box=\box55
\multicol@leftmargin=\dimen171
\c@unbalance=\count294
\c@collectmore=\count295
\doublecol@number=\count296
\multicoltolerance=\count297
\multicolpretolerance=\count298
\full@width=\dimen172
\page@free=\dimen173
\premulticols=\dimen174
\postmulticols=\dimen175
\multicolsep=\skip55
\multicolbaselineskip=\skip56
\partial@page=\box56
\last@line=\box57
\mc@boxedresult=\box58
\maxbalancingoverflow=\dimen176
\mult@rightbox=\box59
\mult@grightbox=\box60
\mult@firstbox=\box61
\mult@gfirstbox=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\c@minrows=\count299
\c@columnbadness=\count300
\c@finalcolumnbadness=\count301
\last@try=\dimen177
\multicolovershoot=\dimen178
\multicolundershoot=\dimen179
\mult@nat@firstbox=\box99
\colbreak@box=\box100
\mc@col@check@num=\count302
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/footmisc/footmisc.sty
Package: footmisc 2024/12/24 v6.0g a miscellany of footnote facilities
\FN@temptoken=\toks28
\footnotemargin=\dimen180
\@outputbox@depth=\dimen181
Package footmisc Info: Declaring symbol style bringhurst on input line 699.
Package footmisc Info: Declaring symbol style chicago on input line 707.
Package footmisc Info: Declaring symbol style wiley on input line 716.
Package footmisc Info: Declaring symbol style lamport-robust on input line 727.

Package footmisc Info: Declaring symbol style lamport* on input line 747.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 768
.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2024/04/01 v1.744(Michael Sharpe) latex and unicode latex su
pport for TeXGyreTermesX

`newtxtext' v1.744, 2024/04/01 Text macros taking advantage of TeXGyre Termes a
nd its extensions (msharpe)
(/usr/local/texlive/2025/texmf-dist/tex/latex/xpatch/xpatch.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 

(/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count303
\l__pdf_internal_box=\box101
))
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands

(/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count304
))
(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks29
\XKV@tempa@toks=\toks30
)
\XKV@depth=\count305
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count306
\xs_countb=\count307
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/carlisle/scalefnt.sty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 24.

(/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count308
\ntx@cnt=\count309

(/usr/local/texlive/2025/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
\tx@sixem=\dimen182
\tx@y=\dimen183
\tx@x=\dimen184
\tx@tmpdima=\dimen185
\tx@tmpdimb=\dimen186
\tx@tmpdimc=\dimen187
\tx@tmpdimd=\dimen188
\tx@tmpdime=\dimen189
\tx@tmpdimf=\dimen190
\tx@dimA=\dimen191
\tx@dimAA=\dimen192
\tx@dimB=\dimen193
\tx@dimBB=\dimen194
\tx@dimC=\dimen195
LaTeX Info: Redefining \oldstylenums on input line 902.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/09/22 v1.754

`newtxmath' v1.754, 2024/09/22 Math macros based originally on txfonts (msharpe
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip57

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks31
\ex@=\dimen196
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen197
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count310
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count311
\leftroot@=\count312
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count313
\DOTSCASE@=\count314
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box102
\strutbox@=\box103
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen198
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count315
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count316
\dotsspace@=\muskip17
\c@parentequation=\count317
\dspbrk@lvl=\count318
\tag@help=\toks32
\row@=\count319
\column@=\count320
\maxfields@=\count321
\andhelp@=\toks33
\eqnshift@=\dimen199
\alignsep@=\dimen256
\tagshift@=\dimen257
\tagwidth@=\dimen258
\totwidth@=\dimen259
\lineht@=\dimen260
\@envbody=\toks34
\multlinegap=\skip58
\multlinetaggap=\skip59
\mathdisplay@stack=\toks35
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count322

(/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count323
\tx@IsAlNum=\count324
\tx@tA=\toks36
\tx@tB=\toks37
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/qhv/m/n --> OT1/qhv/b/n on input line 412.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 423.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/ntxtt/m/n --> OT1/ntxtt/b/n on input line 426.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 535.
\symlettersA=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 582.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 605.
\symAMSm=\mathgroup5
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 630.
\symsymbolsC=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 651.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 6
64.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 664
.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 665.
\symlargesymbolsTXA=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'

(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 679.
\tx@sbptoks=\toks38
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 903.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 904.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 905.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 910.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 911.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 913.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 915.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 919.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 920.
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 966.
LaTeX Font Info:    Redeclaring math accent \dot on input line 991.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 992.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2057.
LaTeX Info: Redefining \Bbbk on input line 2847.
LaTeX Info: Redefining \not on input line 2995.
LaTeX Info: Redefining \textsquare on input line 3025.
LaTeX Info: Redefining \openbox on input line 3027.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pdfpages/pdfpages.sty
Package: pdfpages 2025/01/30 v0.6e Insert pages of external PDF documents (AM)

(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count325
\calc@Bcount=\count326
\calc@Adimen=\dimen261
\calc@Bdimen=\dimen262
\calc@Askip=\skip60
\calc@Bskip=\skip61
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count327
\calc@Cskip=\skip62
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/eso-pic/eso-pic.sty
Package: eso-pic 2023/05/03 v3.0c eso-pic (RN)
\ESO@tempdima=\dimen263
\ESO@tempdimb=\dimen264
)
\AM@pagewidth=\dimen265
\AM@pageheight=\dimen266
\AM@fboxrule=\dimen267

(/usr/local/texlive/2025/texmf-dist/tex/latex/pdfpages/pppdftex.def
File: pppdftex.def 2025/01/30 v0.6e Pdfpages driver for pdfTeX (AM)
)
\pdfpages@includegraphics@status=\count328
\AM@pagebox=\box104
\AM@global@opts=\toks39
\AM@pagecnt=\count329
\AM@toc@title=\toks40
\AM@lof@heading=\toks41
\c@AM@survey=\count330
\AM@templatesizebox=\box105
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen268
\captionmargin=\dimen269
\caption@leftmargin=\dimen270
\caption@rightmargin=\dimen271
\caption@width=\dimen272
\caption@indent=\dimen273
\caption@parindent=\dimen274
\caption@hangindent=\dimen275
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\captionstyle \ifx \@ca
ptype \fig@type \vskip \figcapgap \fi \setbox \@tempboxa \hbox {{\floatlegendst
yle #1\floatcounterend }\capstrut #2}\ifdim \wd \@tempboxa >\hsize {\floatlegen
dstyle #1\floatcounterend }\capstrut #2\par \else \hbox to\hsize {\leftlegendgl
ue \unhbox \@tempboxa \hfil }\fi \ifx \@captype \fig@type \else \vskip \tabcapg
ap \fi  on input line 1175.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count331
\c@continuedfloat=\count332
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count333
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count334
)

LaTeX Warning: \include should only be used after \begin{document} on input lin
e 40.

No file editor/preambleWAD.
(/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen276
\lightrulewidth=\dimen277
\cmidrulewidth=\dimen278
\belowrulesep=\dimen279
\belowbottomsep=\dimen280
\aboverulesep=\dimen281
\abovetopsep=\dimen282
\cmidrulesep=\dimen283
\cmidrulekern=\dimen284
\defaultaddspace=\dimen285
\@cmidla=\count335
\@cmidlb=\count336
\@aboverulesep=\dimen286
\@belowrulesep=\dimen287
\@thisruleclass=\count337
\@lastruleclass=\count338
\@thisrulewidth=\dimen288
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip63
\multirow@cntb=\count339
\multirow@dima=\skip64
\bigstrutjot=\dimen289
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup8
\symAMSb=\mathgroup9
LaTeX Font Info:    Redeclaring math delimiter \ulcorner on input line 74.
LaTeX Font Info:    Redeclaring math delimiter \urcorner on input line 75.
LaTeX Font Info:    Redeclaring math delimiter \llcorner on input line 76.
LaTeX Font Info:    Redeclaring math delimiter \lrcorner on input line 77.
LaTeX Font Info:    Redeclaring math symbol \square on input line 141.
LaTeX Font Info:    Redeclaring math symbol \lozenge on input line 142.
)
LaTeX Font Info:    Redeclaring math symbol \boxdot on input line 44.
LaTeX Font Info:    Redeclaring math symbol \boxplus on input line 45.
LaTeX Font Info:    Redeclaring math symbol \boxtimes on input line 46.
LaTeX Font Info:    Redeclaring math symbol \blacksquare on input line 48.
LaTeX Font Info:    Redeclaring math symbol \centerdot on input line 49.
LaTeX Font Info:    Redeclaring math symbol \blacklozenge on input line 51.
LaTeX Font Info:    Redeclaring math symbol \circlearrowright on input line 52.

LaTeX Font Info:    Redeclaring math symbol \circlearrowleft on input line 53.
LaTeX Font Info:    Redeclaring math symbol \leftrightharpoons on input line 56
.
LaTeX Font Info:    Redeclaring math symbol \boxminus on input line 57.
LaTeX Font Info:    Redeclaring math symbol \Vdash on input line 58.
LaTeX Font Info:    Redeclaring math symbol \Vvdash on input line 59.
LaTeX Font Info:    Redeclaring math symbol \vDash on input line 60.
LaTeX Font Info:    Redeclaring math symbol \twoheadrightarrow on input line 61
.
LaTeX Font Info:    Redeclaring math symbol \twoheadleftarrow on input line 62.

LaTeX Font Info:    Redeclaring math symbol \leftleftarrows on input line 63.
LaTeX Font Info:    Redeclaring math symbol \rightrightarrows on input line 64.

LaTeX Font Info:    Redeclaring math symbol \upuparrows on input line 65.
LaTeX Font Info:    Redeclaring math symbol \downdownarrows on input line 66.
LaTeX Font Info:    Redeclaring math symbol \upharpoonright on input line 67.
LaTeX Font Info:    Redeclaring math symbol \downharpoonright on input line 69.

LaTeX Font Info:    Redeclaring math symbol \upharpoonleft on input line 70.
LaTeX Font Info:    Redeclaring math symbol \downharpoonleft on input line 71.
LaTeX Font Info:    Redeclaring math symbol \rightarrowtail on input line 72.
LaTeX Font Info:    Redeclaring math symbol \leftarrowtail on input line 73.
LaTeX Font Info:    Redeclaring math symbol \leftrightarrows on input line 74.
LaTeX Font Info:    Redeclaring math symbol \rightleftarrows on input line 75.
LaTeX Font Info:    Redeclaring math symbol \Lsh on input line 76.
LaTeX Font Info:    Redeclaring math symbol \Rsh on input line 77.
LaTeX Font Info:    Redeclaring math symbol \leftrightsquigarrow on input line 
79.
LaTeX Font Info:    Redeclaring math symbol \looparrowleft on input line 80.
LaTeX Font Info:    Redeclaring math symbol \looparrowright on input line 81.
LaTeX Font Info:    Redeclaring math symbol \circeq on input line 82.
LaTeX Font Info:    Redeclaring math symbol \succsim on input line 83.
LaTeX Font Info:    Redeclaring math symbol \gtrsim on input line 84.
LaTeX Font Info:    Redeclaring math symbol \gtrapprox on input line 85.
LaTeX Font Info:    Redeclaring math symbol \multimap on input line 86.
LaTeX Font Info:    Redeclaring math symbol \therefore on input line 87.
LaTeX Font Info:    Redeclaring math symbol \because on input line 88.
LaTeX Font Info:    Redeclaring math symbol \doteqdot on input line 89.
LaTeX Font Info:    Redeclaring math symbol \triangleq on input line 91.
LaTeX Font Info:    Redeclaring math symbol \precsim on input line 92.
LaTeX Font Info:    Redeclaring math symbol \lesssim on input line 93.
LaTeX Font Info:    Redeclaring math symbol \lessapprox on input line 94.
LaTeX Font Info:    Redeclaring math symbol \eqslantless on input line 95.
LaTeX Font Info:    Redeclaring math symbol \eqslantgtr on input line 96.
LaTeX Font Info:    Redeclaring math symbol \curlyeqprec on input line 97.
LaTeX Font Info:    Redeclaring math symbol \curlyeqsucc on input line 98.
LaTeX Font Info:    Redeclaring math symbol \preccurlyeq on input line 99.
LaTeX Font Info:    Redeclaring math symbol \leqq on input line 100.
LaTeX Font Info:    Redeclaring math symbol \leqslant on input line 101.
LaTeX Font Info:    Redeclaring math symbol \lessgtr on input line 102.
LaTeX Font Info:    Redeclaring math symbol \backprime on input line 103.
LaTeX Font Info:    Redeclaring math symbol \risingdotseq on input line 104.
LaTeX Font Info:    Redeclaring math symbol \fallingdotseq on input line 105.
LaTeX Font Info:    Redeclaring math symbol \succcurlyeq on input line 106.
LaTeX Font Info:    Redeclaring math symbol \geqq on input line 107.
LaTeX Font Info:    Redeclaring math symbol \geqslant on input line 108.
LaTeX Font Info:    Redeclaring math symbol \gtrless on input line 109.
LaTeX Font Info:    Redeclaring math symbol \bigstar on input line 117.
LaTeX Font Info:    Redeclaring math symbol \between on input line 118.
LaTeX Font Info:    Redeclaring math symbol \blacktriangledown on input line 11
9.
LaTeX Font Info:    Redeclaring math symbol \blacktriangleright on input line 1
20.
LaTeX Font Info:    Redeclaring math symbol \blacktriangleleft on input line 12
1.
LaTeX Font Info:    Redeclaring math symbol \vartriangle on input line 122.
LaTeX Font Info:    Redeclaring math symbol \blacktriangle on input line 123.
LaTeX Font Info:    Redeclaring math symbol \triangledown on input line 124.
LaTeX Font Info:    Redeclaring math symbol \eqcirc on input line 125.
LaTeX Font Info:    Redeclaring math symbol \lesseqgtr on input line 126.
LaTeX Font Info:    Redeclaring math symbol \gtreqless on input line 127.
LaTeX Font Info:    Redeclaring math symbol \lesseqqgtr on input line 128.
LaTeX Font Info:    Redeclaring math symbol \gtreqqless on input line 129.
LaTeX Font Info:    Redeclaring math symbol \Rrightarrow on input line 130.
LaTeX Font Info:    Redeclaring math symbol \Lleftarrow on input line 131.
LaTeX Font Info:    Redeclaring math symbol \veebar on input line 132.
LaTeX Font Info:    Redeclaring math symbol \barwedge on input line 133.
LaTeX Font Info:    Redeclaring math symbol \doublebarwedge on input line 134.
LaTeX Font Info:    Redeclaring math symbol \measuredangle on input line 137.
LaTeX Font Info:    Redeclaring math symbol \sphericalangle on input line 138.
LaTeX Font Info:    Redeclaring math symbol \varpropto on input line 139.
LaTeX Font Info:    Redeclaring math symbol \smallsmile on input line 140.
LaTeX Font Info:    Redeclaring math symbol \smallfrown on input line 141.
LaTeX Font Info:    Redeclaring math symbol \Subset on input line 142.
LaTeX Font Info:    Redeclaring math symbol \Supset on input line 143.
LaTeX Font Info:    Redeclaring math symbol \Cup on input line 144.
LaTeX Font Info:    Redeclaring math symbol \Cap on input line 146.
LaTeX Font Info:    Redeclaring math symbol \curlywedge on input line 148.
LaTeX Font Info:    Redeclaring math symbol \curlyvee on input line 149.
LaTeX Font Info:    Redeclaring math symbol \leftthreetimes on input line 150.
LaTeX Font Info:    Redeclaring math symbol \rightthreetimes on input line 151.

LaTeX Font Info:    Redeclaring math symbol \subseteqq on input line 152.
LaTeX Font Info:    Redeclaring math symbol \supseteqq on input line 153.
LaTeX Font Info:    Redeclaring math symbol \bumpeq on input line 154.
LaTeX Font Info:    Redeclaring math symbol \Bumpeq on input line 155.
LaTeX Font Info:    Redeclaring math symbol \lll on input line 156.
LaTeX Font Info:    Redeclaring math symbol \ggg on input line 158.
LaTeX Font Info:    Redeclaring math symbol \circledS on input line 160.
LaTeX Font Info:    Redeclaring math symbol \pitchfork on input line 161.
LaTeX Font Info:    Redeclaring math symbol \dotplus on input line 162.
LaTeX Font Info:    Redeclaring math symbol \backsim on input line 163.
LaTeX Font Info:    Redeclaring math symbol \backsimeq on input line 164.
LaTeX Font Info:    Redeclaring math symbol \complement on input line 165.
LaTeX Font Info:    Redeclaring math symbol \intercal on input line 166.
LaTeX Font Info:    Redeclaring math symbol \circledcirc on input line 167.
LaTeX Font Info:    Redeclaring math symbol \circledast on input line 168.
LaTeX Font Info:    Redeclaring math symbol \circleddash on input line 169.
LaTeX Font Info:    Redeclaring math symbol \lvertneqq on input line 171.
LaTeX Font Info:    Redeclaring math symbol \gvertneqq on input line 172.
LaTeX Font Info:    Redeclaring math symbol \nleq on input line 173.
LaTeX Font Info:    Redeclaring math symbol \ngeq on input line 174.
LaTeX Font Info:    Redeclaring math symbol \nless on input line 175.
LaTeX Font Info:    Redeclaring math symbol \ngtr on input line 176.
LaTeX Font Info:    Redeclaring math symbol \nprec on input line 177.
LaTeX Font Info:    Redeclaring math symbol \nsucc on input line 178.
LaTeX Font Info:    Redeclaring math symbol \lneqq on input line 179.
LaTeX Font Info:    Redeclaring math symbol \gneqq on input line 180.
LaTeX Font Info:    Redeclaring math symbol \nleqslant on input line 181.
LaTeX Font Info:    Redeclaring math symbol \ngeqslant on input line 182.
LaTeX Font Info:    Redeclaring math symbol \lneq on input line 183.
LaTeX Font Info:    Redeclaring math symbol \gneq on input line 184.
LaTeX Font Info:    Redeclaring math symbol \npreceq on input line 185.
LaTeX Font Info:    Redeclaring math symbol \nsucceq on input line 186.
LaTeX Font Info:    Redeclaring math symbol \precnsim on input line 187.
LaTeX Font Info:    Redeclaring math symbol \succnsim on input line 188.
LaTeX Font Info:    Redeclaring math symbol \lnsim on input line 189.
LaTeX Font Info:    Redeclaring math symbol \gnsim on input line 190.
LaTeX Font Info:    Redeclaring math symbol \nleqq on input line 191.
LaTeX Font Info:    Redeclaring math symbol \ngeqq on input line 192.
LaTeX Font Info:    Redeclaring math symbol \precneqq on input line 193.
LaTeX Font Info:    Redeclaring math symbol \succneqq on input line 194.
LaTeX Font Info:    Redeclaring math symbol \precnapprox on input line 195.
LaTeX Font Info:    Redeclaring math symbol \succnapprox on input line 196.
LaTeX Font Info:    Redeclaring math symbol \lnapprox on input line 197.
LaTeX Font Info:    Redeclaring math symbol \gnapprox on input line 198.
LaTeX Font Info:    Redeclaring math symbol \nsim on input line 199.
LaTeX Font Info:    Redeclaring math symbol \ncong on input line 200.
LaTeX Font Info:    Redeclaring math symbol \diagup on input line 201.
LaTeX Font Info:    Redeclaring math symbol \diagdown on input line 202.
LaTeX Font Info:    Redeclaring math symbol \varsubsetneq on input line 203.
LaTeX Font Info:    Redeclaring math symbol \varsupsetneq on input line 204.
LaTeX Font Info:    Redeclaring math symbol \nsubseteqq on input line 205.
LaTeX Font Info:    Redeclaring math symbol \nsupseteqq on input line 206.
LaTeX Font Info:    Redeclaring math symbol \subsetneqq on input line 207.
LaTeX Font Info:    Redeclaring math symbol \supsetneqq on input line 208.
LaTeX Font Info:    Redeclaring math symbol \varsubsetneqq on input line 209.
LaTeX Font Info:    Redeclaring math symbol \varsupsetneqq on input line 210.
LaTeX Font Info:    Redeclaring math symbol \subsetneq on input line 211.
LaTeX Font Info:    Redeclaring math symbol \supsetneq on input line 212.
LaTeX Font Info:    Redeclaring math symbol \nsubseteq on input line 213.
LaTeX Font Info:    Redeclaring math symbol \nsupseteq on input line 214.
LaTeX Font Info:    Redeclaring math symbol \nparallel on input line 215.
LaTeX Font Info:    Redeclaring math symbol \nmid on input line 216.
LaTeX Font Info:    Redeclaring math symbol \nshortmid on input line 217.
LaTeX Font Info:    Redeclaring math symbol \nshortparallel on input line 218.
LaTeX Font Info:    Redeclaring math symbol \nvdash on input line 219.
LaTeX Font Info:    Redeclaring math symbol \nVdash on input line 220.
LaTeX Font Info:    Redeclaring math symbol \nvDash on input line 221.
LaTeX Font Info:    Redeclaring math symbol \nVDash on input line 222.
LaTeX Font Info:    Redeclaring math symbol \ntrianglerighteq on input line 223
.
LaTeX Font Info:    Redeclaring math symbol \ntrianglelefteq on input line 224.

LaTeX Font Info:    Redeclaring math symbol \ntriangleleft on input line 225.
LaTeX Font Info:    Redeclaring math symbol \ntriangleright on input line 226.
LaTeX Font Info:    Redeclaring math symbol \nleftarrow on input line 227.
LaTeX Font Info:    Redeclaring math symbol \nrightarrow on input line 228.
LaTeX Font Info:    Redeclaring math symbol \nLeftarrow on input line 229.
LaTeX Font Info:    Redeclaring math symbol \nRightarrow on input line 230.
LaTeX Font Info:    Redeclaring math symbol \nLeftrightarrow on input line 231.

LaTeX Font Info:    Redeclaring math symbol \nleftrightarrow on input line 232.

LaTeX Font Info:    Redeclaring math symbol \divideontimes on input line 233.
LaTeX Font Info:    Redeclaring math symbol \varnothing on input line 234.
LaTeX Font Info:    Redeclaring math symbol \nexists on input line 235.
LaTeX Font Info:    Redeclaring math symbol \Finv on input line 236.
LaTeX Font Info:    Redeclaring math symbol \Game on input line 237.
LaTeX Font Info:    Redeclaring math symbol \eth on input line 240.
LaTeX Font Info:    Redeclaring math symbol \eqsim on input line 241.
LaTeX Font Info:    Redeclaring math symbol \beth on input line 242.
LaTeX Font Info:    Redeclaring math symbol \gimel on input line 243.
LaTeX Font Info:    Redeclaring math symbol \daleth on input line 244.
LaTeX Font Info:    Redeclaring math symbol \lessdot on input line 245.
LaTeX Font Info:    Redeclaring math symbol \gtrdot on input line 246.
LaTeX Font Info:    Redeclaring math symbol \ltimes on input line 247.
LaTeX Font Info:    Redeclaring math symbol \rtimes on input line 248.
LaTeX Font Info:    Redeclaring math symbol \shortmid on input line 249.
LaTeX Font Info:    Redeclaring math symbol \shortparallel on input line 250.
LaTeX Font Info:    Redeclaring math symbol \smallsetminus on input line 251.
LaTeX Font Info:    Redeclaring math symbol \thicksim on input line 252.
LaTeX Font Info:    Redeclaring math symbol \thickapprox on input line 253.
LaTeX Font Info:    Redeclaring math symbol \approxeq on input line 254.
LaTeX Font Info:    Redeclaring math symbol \succapprox on input line 255.
LaTeX Font Info:    Redeclaring math symbol \precapprox on input line 256.
LaTeX Font Info:    Redeclaring math symbol \curvearrowleft on input line 257.
LaTeX Font Info:    Redeclaring math symbol \curvearrowright on input line 258.

LaTeX Font Info:    Redeclaring math symbol \digamma on input line 259.
LaTeX Font Info:    Redeclaring math symbol \varkappa on input line 260.


! LaTeX Error: Command `\Bbbk' already defined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.261 ...ol{\Bbbk}           {\mathord}{AMSb}{"7C}
                                                  
? 
LaTeX Font Info:    Redeclaring math symbol \hslash on input line 262.
LaTeX Font Info:    Redeclaring math symbol \backepsilon on input line 265.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/appendix/appendix.sty
Package: appendix 2020/02/08 v1.2c extra appendix facilities
\c@@pps=\count340
\c@@ppsavesec=\count341
\c@@ppsaveapp=\count342
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ncctools/manyfoot.sty
Package: manyfoot 2019/08/03 v1.11 Many Footnote Levels Package (NCC)

(/usr/local/texlive/2025/texmf-dist/tex/latex/ncctools/nccfoots.sty
Package: nccfoots 2005/02/03 v1.2 NCC Footnotes Package (NCC)
)
\MFL@columnwidth=\dimen290
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen291
\ar@mcellbox=\box106
\extrarowheight=\dimen292
\NC@list=\toks42
\extratabsurround=\skip65
\backup@length=\skip66
\ar@cellbox=\box107
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count343
\float@exts=\toks43
\float@box=\box108
\@float@everytoks=\toks44
\@floatcapt=\box109
)
\@float@every@algorithm=\toks45
\c@algorithm=\count344
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count345
\c@ALG@rem=\count346
\c@ALG@nested=\count347
\ALG@tlm=\skip67
\ALG@thistlm=\skip68
\c@ALG@Lnr=\count348
\c@ALG@blocknr=\count349
\c@ALG@storecount=\count350
\c@ALG@tmpcounter=\count351
\ALG@tmplength=\skip69
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

Document Style - pseudocode environments for use with the `algorithmicx' style
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count352
\lst@gtempboxa=\box110
\lst@token=\toks46
\lst@length=\count353
\lst@currlwidth=\dimen293
\lst@column=\count354
\lst@pos=\count355
\lst@lostspace=\dimen294
\lst@width=\dimen295
\lst@newlines=\count356
\lst@lineno=\count357
\lst@maxwidth=\dimen296

(/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count358
\lst@skipnumbers=\count359
\lst@framebox=\box111
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/euscript.sty
Package: euscript 2009/06/22 v3.00 Euler Script fonts
LaTeX Font Info:    Overwriting math alphabet `\EuScript' in version `bold'
(Font)                  U/eus/m/n --> U/eus/b/n on input line 33.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)
\TX@col@width=\dimen297
\TX@old@table=\dimen298
\TX@old@col=\dimen299
\TX@target=\dimen300
\TX@delta=\dimen301
\TX@cols=\count360
\TX@ftn=\toks47
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
\everycr=\toks48
\minrowclearance=\skip70
\rownum=\count361
)
\@indexfile=\write4
\openout4 = `editor.idx'.


Writing index file editor.idx
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input lin
e 72.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 10.0pt on input line 72.
 (./editor.aux (./dedication.aux) (./foreword.aux) (./preface.aux) (./acknowled
gement.aux)
(./contriblist.aux) (./acronym.aux) (./part03.aux) (./author301.aux)
(./author302.aux

LaTeX Warning: Label `1' multiply defined.


LaTeX Warning: Label `2' multiply defined.


LaTeX Warning: Label `3' multiply defined.


LaTeX Warning: Label `4' multiply defined.


LaTeX Warning: Label `5' multiply defined.


LaTeX Warning: Label `6' multiply defined.


LaTeX Warning: Label `7' multiply defined.


LaTeX Warning: Label `8' multiply defined.


LaTeX Warning: Label `9' multiply defined.


LaTeX Warning: Label `10' multiply defined.


LaTeX Warning: Label `11' multiply defined.


LaTeX Warning: Label `12' multiply defined.


LaTeX Warning: Label `13' multiply defined.


LaTeX Warning: Label `14' multiply defined.


LaTeX Warning: Label `15' multiply defined.


LaTeX Warning: Label `16' multiply defined.


LaTeX Warning: Label `17' multiply defined.


LaTeX Warning: Label `18' multiply defined.


LaTeX Warning: Label `19' multiply defined.


LaTeX Warning: Label `20' multiply defined.

) (./appendix.aux) (./glossary.aux))
\openout1 = `editor.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 72.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line
 72.

(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 72.
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 72.
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 72.
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 72.
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 72.
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 72.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 
72.

(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 72.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line
 72.

(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 72.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 72.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input lin
e 72.

(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 72.

(/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count362
\scratchdimen=\dimen302
\scratchbox=\box112
\nofMPsegments=\count363
\nofMParguments=\count364
\everyMPshowfont=\toks49
\MPscratchCnt=\count365
\MPscratchDim=\dimen303
\MPnumerator=\count366
\makeMPintoPDFobject=\count367
\everyMPtoPDFconversion=\toks50
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
\c@mv@tabular=\count368
\c@mv@boldtabular=\count369
LaTeX Info: Command `\dddot' is already robust on input line 72.
LaTeX Info: Command `\ddddot' is already robust on input line 72.

(/usr/local/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape.sty
Package: pdflscape 2022-10-27 v0.13 Display of landscape pages in PDF

(/usr/local/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape-nometadata.st
y
Package: pdflscape-nometadata 2022-10-28 v0.13 Display of landscape pages in PD
F (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/lscape.sty
Package: lscape 2020/05/28 v3.02 Landscape Pages (DPC)
)
Package pdflscape Info: Auto-detected driver: pdftex on input line 81.
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: listings package is loaded.
Package caption Info: End \AtBeginDocument code.
\c@lstlisting=\count370
\openout2 = `dedication.aux'.


(./dedication.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 12.0pt on input line 9.


[5






{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/loc
al/texlive/2025/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc}])
\openout2 = `foreword.aux'.


(./foreword.tex

[6






]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 16.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 16.0pt on input line 9.
LaTeX Font Info:    Trying to load font information for OT1+minntx on input lin
e 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 
9.

(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2024/04/09 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 
9.

(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 
9.

(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+msa on input line 9.

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 9.

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 10.0pt on input line 10.

Overfull \hbox (3.95215pt too wide) in paragraph at lines 10--11
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 fore-word.tex \OT1/min
ntx/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

)

LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.5pt on input line 77.
[7]
\openout2 = `preface.aux'.

 (./preface.tex

[8





])

[9]
\openout2 = `acknowledgement.aux'.

 (./acknowledgement.tex

[10





])

[11]

[12




] (./editor.toc
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 10.0pt on input line 1.


[13])
\tf@toc=\write5
\openout5 = `editor.toc'.



[14]
\openout2 = `contriblist.aux'.

 (./contriblist.tex)

[15



]
\openout2 = `acronym.aux'.

 (./acronym.tex

[16





]
Overfull \hbox (3.22217pt too wide) in paragraph at lines 10--11
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 acronym.tex \OT1/minnt
x/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

LaTeX Font Info:    Trying to load font information for OT1+ntxtt on input line
 12.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtt.fd
File: ot1ntxtt.fd 2012/04/20 v3.1
)
LaTeX Font Info:    Font shape `OT1/ntxtt/m/n' will be
(Font)              scaled to size 10.0pt on input line 12.
)

[17]

[18




]
\openout2 = `part03.aux'.

 (./part03.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 18.0pt on input line 10.


[1



]

[2])
\openout2 = `author301.aux'.

 (./author301.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 14.0pt on input line 55.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 14.0pt on input line 55.
LaTeX Font Info:    Calculating math sizes for size <8.5> on input line 55.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 8.5pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 6.20496pt on input line 55.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 4.67502pt on input line 55.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 61.


[3





]

[4]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 11.0pt on input line 84.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 11.0pt on input line 84.


[5]

[6]
<fig301/1.png, id=71, 1592.95125pt x 531.9875pt>
File: fig301/1.png Graphic file (type png)
<use fig301/1.png>
Package pdftex.def Info: fig301/1.png  used on input line 122.
(pdftex.def)             Requested size: 299.60547pt x 100.05586pt.


[7 <./fig301/1.png>]
<fig301/2.png, id=77, 939.51pt x 855.195pt>
File: fig301/2.png Graphic file (type png)
<use fig301/2.png>
Package pdftex.def Info: fig301/2.png  used on input line 153.
(pdftex.def)             Requested size: 299.60547pt x 272.71541pt.


[8]
<fig301/3.png, id=81, 897.3525pt x 445.665pt>
File: fig301/3.png Graphic file (type png)
<use fig301/3.png>
Package pdftex.def Info: fig301/3.png  used on input line 167.
(pdftex.def)             Requested size: 332.89723pt x 165.32867pt.


[9 <./fig301/2.png>]

[10 <./fig301/3.png>]
<fig301/4a.png, id=90, 479.7925pt x 336.25626pt>
File: fig301/4a.png Graphic file (type png)
<use fig301/4a.png>
Package pdftex.def Info: fig301/4a.png  used on input line 205.
(pdftex.def)             Requested size: 159.78925pt x 111.98595pt.
<fig301/4b.png, id=91, 469.755pt x 369.38pt>
File: fig301/4b.png Graphic file (type png)
<use fig301/4b.png>
Package pdftex.def Info: fig301/4b.png  used on input line 212.
(pdftex.def)             Requested size: 159.78925pt x 125.64389pt.


[11 <./fig301/4a.png> <./fig301/4b.png>]

[12]

[13]
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 8.5pt on input line 309.
 (./references301.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.5pt on input line 4.


[14]))

[15]
\openout2 = `author302.aux'.

 (./author302.tex

[16





]

[17]
<fig302/1.png, id=117, 699.048pt x 530.418pt>
File: fig302/1.png Graphic file (type png)
<use fig302/1.png>
Package pdftex.def Info: fig302/1.png  used on input line 62.
(pdftex.def)             Requested size: 299.60547pt x 227.33069pt.


[18 <./fig302/1.png>]
<fig302/2.png, id=122, 5437.31375pt x 2476.25125pt>
File: fig302/2.png Graphic file (type png)
<use fig302/2.png>
Package pdftex.def Info: fig302/2.png  used on input line 79.
(pdftex.def)             Requested size: 299.60547pt x 136.43983pt.


[19 <./fig302/2.png>]
Underfull \hbox (badness 4752) in paragraph at lines 98--98
[]|\OT1/minntx/m/n/8.5 Bedside mon-i-tor
 []



[20]
<fig302/3a.png, id=130, 3308.36pt x 2484.28125pt>
File: fig302/3a.png Graphic file (type png)
<use fig302/3a.png>
Package pdftex.def Info: fig302/3a.png  used on input line 142.
(pdftex.def)             Requested size: 163.12146pt x 122.47762pt.
<fig302/3b.png, id=131, 1927.2pt x 1445.4pt>
File: fig302/3b.png Graphic file (type png)
<use fig302/3b.png>
Package pdftex.def Info: fig302/3b.png  used on input line 148.
(pdftex.def)             Requested size: 163.12146pt x 122.33908pt.


[21]
<fig302/4.png, id=135, 4456.65pt x 1449.415pt>
File: fig302/4.png Graphic file (type png)
<use fig302/4.png>
Package pdftex.def Info: fig302/4.png  used on input line 175.
(pdftex.def)             Requested size: 332.89723pt x 108.25911pt.


[22 <./fig302/3a.png> <./fig302/3b.png> <./fig302/4.png>]
<fig302/5a.png, id=140, 2484.28125pt x 2484.28125pt>
File: fig302/5a.png Graphic file (type png)
<use fig302/5a.png>
Package pdftex.def Info: fig302/5a.png  used on input line 202.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/5b.png, id=141, 2484.28125pt x 2484.28125pt>
File: fig302/5b.png Graphic file (type png)
<use fig302/5b.png>
Package pdftex.def Info: fig302/5b.png  used on input line 209.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/5c.png, id=142, 2484.28125pt x 2484.28125pt>
File: fig302/5c.png Graphic file (type png)
<use fig302/5c.png>
Package pdftex.def Info: fig302/5c.png  used on input line 216.
(pdftex.def)             Requested size: 106.52954pt x 106.51875pt.
<fig302/6a.jpg, id=143, 642.4pt x 642.4pt>
File: fig302/6a.jpg Graphic file (type jpg)
<use fig302/6a.jpg>
Package pdftex.def Info: fig302/6a.jpg  used on input line 228.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/6b.jpg, id=144, 642.4pt x 642.4pt>
File: fig302/6b.jpg Graphic file (type jpg)
<use fig302/6b.jpg>
Package pdftex.def Info: fig302/6b.jpg  used on input line 235.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/6c.jpg, id=145, 642.4pt x 642.4pt>
File: fig302/6c.jpg Graphic file (type jpg)
<use fig302/6c.jpg>
Package pdftex.def Info: fig302/6c.jpg  used on input line 242.
(pdftex.def)             Requested size: 106.52954pt x 106.52074pt.
<fig302/7a.png, id=146, 617.30624pt x 462.72874pt>
File: fig302/7a.png Graphic file (type png)
<use fig302/7a.png>
Package pdftex.def Info: fig302/7a.png  used on input line 253.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/7b.png, id=147, 617.30624pt x 462.72874pt>
File: fig302/7b.png Graphic file (type png)
<use fig302/7b.png>
Package pdftex.def Info: fig302/7b.png  used on input line 258.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/7c.png, id=148, 617.30624pt x 462.72874pt>
File: fig302/7c.png Graphic file (type png)
<use fig302/7c.png>
Package pdftex.def Info: fig302/7c.png  used on input line 264.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.
<fig302/7d.png, id=149, 617.30624pt x 462.72874pt>
File: fig302/7d.png Graphic file (type png)
<use fig302/7d.png>
Package pdftex.def Info: fig302/7d.png  used on input line 269.
(pdftex.def)             Requested size: 163.12146pt x 122.2695pt.


[23]
<fig302/8.png, id=156, 4227.795pt x 1593.955pt>
File: fig302/8.png Graphic file (type png)
<use fig302/8.png>
Package pdftex.def Info: fig302/8.png  used on input line 296.
(pdftex.def)             Requested size: 332.89723pt x 125.50029pt.


[24 <./fig302/5a.png> <./fig302/5b.png> <./fig302/5c.png> <./fig302/6a.jpg> <./
fig302/6b.jpg> <./fig302/6c.jpg>]

[25 <./fig302/7a.png> <./fig302/7b.png> <./fig302/7c.png> <./fig302/7d.png> <./
fig302/8.png>]
<fig302/9.png, id=171, 2750.275pt x 2128.95375pt>
File: fig302/9.png Graphic file (type png)
<use fig302/9.png>
Package pdftex.def Info: fig302/9.png  used on input line 323.
(pdftex.def)             Requested size: 332.89723pt x 257.67242pt.


[26 <./fig302/9.png>]

[27]

[28]

[29]
Underfull \hbox (badness 10000) in paragraph at lines 447--447
[]|\OT1/minntx/m/n/8.5 Automated Car-dio-vas-cu-lar
 []


Underfull \hbox (badness 10000) in paragraph at lines 447--447
[]|\OT1/minntx/m/n/8.5 Ensemble deep
 []


Underfull \hbox (badness 5119) in paragraph at lines 448--448
[]|\OT1/minntx/m/n/8.5 Risk As-sess-ment for Is-
 []


Underfull \hbox (badness 4899) in paragraph at lines 448--448
[]|\OT1/minntx/m/n/8.5 XGBoost, ma-chine
 []


Underfull \hbox (badness 10000) in paragraph at lines 448--448
[]|\OT1/minntx/m/n/8.5 AUROC: 0.86,
 []


Underfull \hbox (badness 10000) in paragraph at lines 450--450
[]|\OT1/minntx/m/n/8.5 Comprehensive Non-
 []


Underfull \hbox (badness 10000) in paragraph at lines 450--450
\OT1/minntx/m/n/8.5 Invasive Di-ag-no-sis of
 []


Underfull \hbox (badness 7064) in paragraph at lines 450--450
\OT1/minntx/m/n/8.5 Coro-nary Artery Dis-ease
 []

<fig302/10a.png, id=186, 617.30624pt x 462.72874pt>
File: fig302/10a.png Graphic file (type png)
<use fig302/10a.png>
Package pdftex.def Info: fig302/10a.png  used on input line 463.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10b.png, id=187, 617.30624pt x 462.72874pt>
File: fig302/10b.png Graphic file (type png)
<use fig302/10b.png>
Package pdftex.def Info: fig302/10b.png  used on input line 469.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10c.png, id=188, 617.30624pt x 462.72874pt>
File: fig302/10c.png Graphic file (type png)
<use fig302/10c.png>
Package pdftex.def Info: fig302/10c.png  used on input line 475.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10d.png, id=189, 617.30624pt x 462.72874pt>
File: fig302/10d.png Graphic file (type png)
<use fig302/10d.png>
Package pdftex.def Info: fig302/10d.png  used on input line 481.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10e.png, id=190, 617.30624pt x 462.72874pt>
File: fig302/10e.png Graphic file (type png)
<use fig302/10e.png>
Package pdftex.def Info: fig302/10e.png  used on input line 487.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.
<fig302/10f.png, id=191, 617.30624pt x 462.72874pt>
File: fig302/10f.png Graphic file (type png)
<use fig302/10f.png>
Package pdftex.def Info: fig302/10f.png  used on input line 493.
(pdftex.def)             Requested size: 159.78925pt x 119.77708pt.


[30]
<fig302/11a.png, id=195, 617.30624pt x 462.72874pt>
File: fig302/11a.png Graphic file (type png)
<use fig302/11a.png>
Package pdftex.def Info: fig302/11a.png  used on input line 505.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.
<fig302/11b.png, id=196, 617.30624pt x 462.72874pt>
File: fig302/11b.png Graphic file (type png)
<use fig302/11b.png>
Package pdftex.def Info: fig302/11b.png  used on input line 511.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.
<fig302/11c.png, id=197, 617.30624pt x 462.72874pt>
File: fig302/11c.png Graphic file (type png)
<use fig302/11c.png>
Package pdftex.def Info: fig302/11c.png  used on input line 517.
(pdftex.def)             Requested size: 106.52954pt x 79.84903pt.


[31 <./fig302/10a.png> <./fig302/10b.png> <./fig302/10c.png> <./fig302/10d.png>
 <./fig302/10e.png> <./fig302/10f.png>] (./references302.tex

[32 <./fig302/11a.png> <./fig302/11b.png> <./fig302/11c.png>]
Underfull \hbox (badness 10000) in paragraph at lines 40--41
[]\OT1/minntx/m/n/8.5 "Vital signs," \OT1/ntxtlf/m/it/8.5 Wikipedia, The Free E
n-cy-clo-pe-dia\OT1/minntx/m/n/8.5 . Avail-able on-line:
 []



[33]))

[34]
\openout2 = `appendix.aux'.

 (./appendix.tex
Appendix A.

Overfull \hbox (5.14217pt too wide) in paragraph at lines 14--15
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 ap-pendix.tex \OT1/min
ntx/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 7.3pt on input line 24.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 10.0pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 7.3pt on input line 33.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 5.5pt on input line 33.
Package epstopdf Info: Source file: <figure.eps>
(epstopdf)                    date: 2025-05-30 09:09:04
(epstopdf)                    size: 62739 bytes
(epstopdf)             Output file: <figure-eps-converted-to.pdf>
(epstopdf)                    date: 2025-05-30 09:09:04
(epstopdf)                    size: 13942 bytes
(epstopdf)             Command: <repstopdf --outfile=figure-eps-converted-to.pd
f figure.eps>
(epstopdf)             \includegraphics on input line 55.
Package epstopdf Info: Output file is already uptodate.
<figure-eps-converted-to.pdf, id=219, 139.52126pt x 195.73125pt>
File: figure-eps-converted-to.pdf Graphic file (type pdf)
<use figure-eps-converted-to.pdf>
Package pdftex.def Info: figure-eps-converted-to.pdf  used on input line 55.
(pdftex.def)             Requested size: 90.68773pt x 127.22379pt.


[35









]
Overfull \hbox (0.61853pt too wide) in paragraph at lines 67--78
[][]
 []

)

[36 <./figure-eps-converted-to.pdf>]
\openout2 = `glossary.aux'.

 (./glossary.tex)

[37





]
No file editor.ind.
(./editor.aux (./dedication.aux) (./foreword.aux) (./preface.aux)
(./acknowledgement.aux) (./contriblist.aux) (./acronym.aux) (./part03.aux)
(./author301.aux) (./author302.aux) (./appendix.aux) (./glossary.aux))
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2020/03/25>
 ***********


LaTeX Warning: There were multiply-defined labels.

 ) 
Here is how much of TeX's memory you used:
 13877 strings out of 473190
 213620 string characters out of 5715800
 635677 words of memory out of 5000000
 36781 multiletter control sequences out of 15000+600000
 623434 words of font info for 168 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 83i,12n,131p,1390b,605s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/NewTXMI.pfb></us
r/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/NewTXMI5.pfb></usr/loc
al/texlive/2025/texmf-dist/fonts/type1/public/newtx/txmiaX.pfb></usr/local/texl
ive/2025/texmf-dist/fonts/type1/public/newtx/txsys.pfb></usr/local/texlive/2025
/texmf-dist/fonts/type1/public/txfonts/txtt.pfb></usr/local/texlive/2025/texmf-
dist/fonts/type1/public/newtx/ztmb.pfb></usr/local/texlive/2025/texmf-dist/font
s/type1/public/newtx/ztmr.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/p
ublic/newtx/ztmri.pfb>
Output written on editor.pdf (51 pages, 18191528 bytes).
PDF statistics:
 272 PDF objects out of 1000 (max. 8388607)
 143 compressed objects within 2 object streams
 0 named destinations out of 1000 (max. 500000)
 161 words of extra memory for PDF output out of 10000 (max. 10000000)

